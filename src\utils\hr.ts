/**
 * 高亮关键字
 * @param node 节点
 * @param pattern 匹配的正则表达式
 * @param type 高亮关键字的key
 * @param index - 可选。本项目中特定的需求，表示第几组关键词
 * @returns exposeCount - 露出次数
 */
export function highlightKeyword(node, pattern, type, index?) {
  var exposeCount = 0;
  // 文本节点
  if (node.nodeType === 3) {
    // node.data为文本节点的文本内容
    var matchResult = node.data.match(pattern);
    // 有匹配上的话
    if (matchResult) {
      // 创建一个span节点，用来包裹住匹配到的关键词内容
      var highlightEl = document.createElement("span");
      // 不用类名来控制高亮，用自定义属性data-*来标识，
      // 比用类名更减少概率与原本内容重名，避免样式覆盖
      highlightEl.dataset.highlight = "yes";
      highlightEl.dataset.type = type;

      highlightEl.dataset.highlightMatch = matchResult[0];

      // 记录第几组关键词
      if (index) {
        highlightEl.dataset.highlightIndex = index;
      }

      // 从匹配到的初始位置开始截断到原本节点末尾，产生新的文本节点
      var matchNode = node.splitText(matchResult.index);
      // 从新的文本节点中再次截断，按照匹配到的关键词的长度开始截断，
      // 此时0-length之间的文本作为matchNode的文本内容
      matchNode.splitText(matchResult[0].length);
      // 对matchNode这个文本节点的内容（即匹配到的关键词内容）创建出一个新的文本节点出来
      var highlightTextNode = document.createTextNode(matchNode.data);
      // 插入到创建的span节点中
      highlightEl.appendChild(highlightTextNode);
      // 把原本matchNode这个节点替换成用于标记高亮的span节点
      matchNode.parentNode.replaceChild(highlightEl, matchNode);
      exposeCount++;
    }
  }
  // 具体条件自己加，这里是基础条件
  else if (
    node.nodeType === 1 &&
    !/script|style/.test(node.tagName.toLowerCase())
  ) {
    if (node.dataset.highlight === "yes") {
      if (index == null) {
        return;
      }
      if (node.dataset.highlightIndex === index.toString()) {
        return;
      }
    }
    // 遍历该节点的所有子孙节点，找出文本节点进行高亮标记
    let childNodes = node.childNodes;
    for (var i = 0; i < childNodes.length; i++) {
      highlightKeyword(childNodes[i], pattern, type, index);
    }
  }
  return exposeCount;
}

/**
 * 替换关键字
 * @param node 节点
 * @param pattern 匹配的正则表达式
 * @param index - 可选。本项目中特定的需求，表示第几组关键词
 * @returns exposeCount - 露出次数
 */
export function replaceKeyword(node, pattern, replacement) {
  var replacedCount = 0;

  // 文本节点
  if (node.nodeType === 3) {
    // node.data为文本节点的文本内容
    var matchResult = node.data.match(pattern);
    // 有匹配上的话
    if (matchResult) {
      // 创建一个span节点，用来包裹住替换后的关键词内容
      var replaceEl = document.createElement("span");
      replaceEl.textContent = matchResult[0].replace(pattern, replacement);

      // 从匹配到的初始位置开始截断到原本节点末尾，产生新的文本节点
      var matchNode = node.splitText(matchResult.index);
      // 从新的文本节点中再次截断，按照匹配到的关键词的长度开始截断，
      // 此时0-length之间的文本作为matchNode的文本内容
      matchNode.splitText(matchResult[0].length);
      // 把原本matchNode这个节点替换成用于替换的span节点
      matchNode.parentNode.replaceChild(replaceEl, matchNode);
      replacedCount++;
    }
  }
  // 具体条件自己加，这里是基础条件
  else if (
    node.nodeType === 1 &&
    !/script|style/.test(node.tagName.toLowerCase())
  ) {
    // 遍历该节点的所有子孙节点，找出文本节点进行替换
    let childNodes = node.childNodes;
    for (var i = 0; i < childNodes.length; i++) {
      replacedCount += replaceKeyword(childNodes[i], pattern, replacement);
    }
  }
  return replacedCount;
}

/**
 * @param {String | Array} keywords - 要高亮的关键词或关键词数组
 * @returns {Array}
 */
export function handleKeyword(keywords) {
  var wordMatchString = "";
  var words = [].concat(keywords);
  words.forEach(item => {
    let transformString = item.replace(/[.[*?+^$|()/]|\]|\\/g, "\\$&");
    wordMatchString += `|(${transformString})`;
  });
  wordMatchString = wordMatchString.substring(1);
  // 用于再次高亮与关闭的关键字作为一个整体的匹配正则
  var wholePattern = new RegExp(`^${wordMatchString}$`, "i");
  // 用于第一次高亮的关键字匹配正则
  var pattern = new RegExp(wordMatchString, "i");
  return [pattern, wholePattern];
}

/**
 * @param pattern 匹配的正则表达式
 * @param index 关键词的组别，即第几组关键词
 */
export function closeHighlight(pattern, index, node, type) {
  var highlightNodeList;
  if (type === "all") {
    highlightNodeList = node.querySelectorAll(`[data-highlight=yes]`);
  } else {
    highlightNodeList = node.querySelectorAll(`[data-type="${type}"]`);
  }
  for (var n = 0; n < highlightNodeList.length; n++) {
    // highlightNodeList[n].dataset.highlight = "no";
    const dataset = highlightNodeList[n].dataset;
    // 如果不需要分组或分组了组别不对，则不需要取消
    if (!index || dataset.highlightIndex !== index.toString()) {
      return;
    }

    if (pattern.test(dataset.highlightMatch)) {
      var parentNode = highlightNodeList[n].parentNode;
      var childNodes = highlightNodeList[n].childNodes;
      var childNodesLen = childNodes.length;
      var nextSibling = highlightNodeList[n].nextSibling;
      for (var k = 0; k < childNodesLen; k++) {
        parentNode.insertBefore(childNodes[0], nextSibling);
      }
      var flagNode = document.createTextNode("");
      parentNode.replaceChild(flagNode, highlightNodeList[n]);
      parentNode.normalize();
    }
  }
}

/**
 * 高亮关键字
 * @param node 节点
 * @param pattern 匹配的正则表达式
 * @param type 高亮关键字的key
 * @param index - 可选。本项目中特定的需求，表示第几组关键词
 * @returns exposeCount - 露出次数
 */
export function highlightCommonKeyword(
  node,
  pattern,
  type,
  index,
  exposeCount = 0
) {
  // 文本节点
  if (node.nodeType === 3) {
    // node.data为文本节点的文本内容
    var matchResult = node.data.match(pattern);
    // 有匹配上的话
    if (matchResult) {
      // 高亮已过滤数组
      // 创建一个span节点，用来包裹住匹配到的关键词内容
      var highlightEl = document.createElement("span");
      // 不用类名来控制高亮，用自定义属性data-*来标识，
      // 比用类名更减少概率与原本内容重名，避免样式覆盖
      highlightEl.dataset.highlight = "yes";
      highlightEl.dataset.type = type;
      highlightEl.dataset.idx = String(exposeCount);

      highlightEl.dataset.highlightMatch = matchResult[0];

      // 记录第几组关键词
      if (index) {
        highlightEl.dataset.highlightIndex = index;
      }

      // 从匹配到的初始位置开始截断到原本节点末尾，产生新的文本节点
      var matchNode = node.splitText(matchResult.index);
      // 从新的文本节点中再次截断，按照匹配到的关键词的长度开始截断，
      // 此时0-length之间的文本作为matchNode的文本内容
      matchNode.splitText(matchResult[0].length);
      // 对matchNode这个文本节点的内容（即匹配到的关键词内容）创建出一个新的文本节点出来
      var highlightTextNode = document.createTextNode(matchNode.data);
      // 插入到创建的span节点中
      highlightEl.appendChild(highlightTextNode);
      // 把原本matchNode这个节点替换成用于标记高亮的span节点
      matchNode.parentNode.replaceChild(highlightEl, matchNode);
      exposeCount++;
    }
  }
  // 具体条件自己加，这里是基础条件
  else if (
    node.nodeType === 1 &&
    !/script|style/.test(node.tagName.toLowerCase())
  ) {
    if (node.dataset.highlight === "yes") {
      // 既是敏感词也是替换词，已经高亮过，继承idx
      if (pattern.test(node.textContent)) {
        exposeCount = Math.max(exposeCount, parseInt(node.dataset.idx) + 1);
        return exposeCount;
      }
      if (index == null) {
        return exposeCount;
      }
      if (node.dataset.highlightIndex === index.toString()) {
        return exposeCount;
      }
    }
    // 遍历该节点的所有子孙节点，找出文本节点进行高亮标记
    let childNodes = node.childNodes;
    for (var i = 0; i < childNodes.length; i++) {
      exposeCount = highlightCommonKeyword(
        childNodes[i],
        pattern,
        type,
        index,
        exposeCount
      );
    }
  }
  return exposeCount;
}

/**
 * @param pattern 匹配的正则表达式
 * @param index 关键词的组别，即第几组关键词
 * @param keyword 关键词
 * @param idx 关闭指定元素
 * @param type 关键词高亮类型
 */
export function closeTargetHighlight(pattern, index, keyword, idx, type) {
  return new Promise((resolve, reject) => {
    try {
      const ifr = document.querySelector("iframe");
      const body = ifr.contentDocument.getElementsByTagName("body")[0];
      var highlightNodeList = Array.from(
        body.querySelectorAll(`[data-highlight-match="${keyword}"]`)
      ).filter(node => {
        return (
          node.getAttribute("data-type") === type &&
          node.getAttribute("data-idx") == idx
        );
      });

      for (var n = 0; n < highlightNodeList.length; n++) {
        const dataset = (highlightNodeList[n] as HTMLElement).dataset;
        // 如果不需要分组或分组了组别不对，则不需要取消
        if (!index || dataset.highlightIndex !== index.toString()) {
          return;
        }

        if (pattern.test(dataset.highlightMatch)) {
          var parentNode = highlightNodeList[n].parentNode;
          var childNodes = highlightNodeList[n].childNodes;
          var childNodesLen = childNodes.length;
          var nextSibling = highlightNodeList[n].nextSibling;
          for (var k = 0; k < childNodesLen; k++) {
            parentNode.insertBefore(childNodes[0], nextSibling);
          }
          var flagNode = document.createTextNode("");
          parentNode.replaceChild(flagNode, highlightNodeList[n]);
          parentNode.normalize();
        }
      }
      resolve(true);
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * 替换指定关键字
 * @param node 节点
 * @param pattern 匹配的正则表达式
 * @param type 高亮关键字的key
 * @param index - 可选。本项目中特定的需求，表示第几组关键词
 * @returns void
 */
export function replaceTargetKeyword(
  node,
  pattern,
  type,
  index,
  idx,
  replacement
) {
  // 文本节点
  if (node.nodeType === 3) {
    // node.data为文本节点的文本内容
    var matchResult = node.data.match(pattern);
    // 有匹配上的话
    if (matchResult) {
      if (node.parentElement.dataset.idx === idx.toString()) {
        // 创建一个span节点，用来包裹住替换后的关键词内容
        var replaceEl = document.createElement("span");
        replaceEl.textContent = matchResult[0].replace(pattern, replacement);

        node.parentElement.parentNode.replaceChild(
          replaceEl,
          node.parentElement
        );
      }
    }
  }
  // 具体条件自己加，这里是基础条件
  else if (
    node.nodeType === 1 &&
    !/script|style/.test(node.tagName.toLowerCase())
  ) {
    // 遍历该节点的所有子孙节点，找出文本节点进行替换
    let childNodes = node.childNodes;
    for (var i = 0; i < childNodes.length; i++) {
      replaceTargetKeyword(
        childNodes[i],
        pattern,
        type,
        index,
        idx,
        replacement
      );
    }
  }

  return node;
}

/**
 * 替换指定关键字
 * @param node 节点
 * @param pattern 匹配的正则表达式
 * @param type 高亮关键字的key
 * @returns void
 */
export function replaceAllKeyword(node, pattern, type, index, replacement) {
  // 文本节点
  if (node.nodeType === 3) {
    // node.data为文本节点的文本内容
    var matchResult = node.data.match(pattern);
    // 有匹配上的话
    if (matchResult) {
      if (node.parentElement.dataset.type) {
        // 创建一个span节点，用来包裹住替换后的关键词内容
        var replaceEl = document.createElement("span");
        replaceEl.textContent = matchResult[0].replace(pattern, replacement);

        node.parentElement.parentNode.replaceChild(
          replaceEl,
          node.parentElement
        );
      }
    }
  }
  // 具体条件自己加，这里是基础条件
  else if (
    node.nodeType === 1 &&
    !/script|style/.test(node.tagName.toLowerCase())
  ) {
    // 遍历该节点的所有子孙节点，找出文本节点进行替换
    let childNodes = node.childNodes;
    for (var i = 0; i < childNodes.length; i++) {
      replaceAllKeyword(childNodes[i], pattern, type, index, replacement);
    }
  }

  return node;
}

/**
 * @param pattern 匹配的正则表达式
 * @param index 关键词的组别，即第几组关键词
 */
export function initCloseHighlight() {
  const ifr = document.querySelector("iframe");
  const body = ifr.contentDocument.getElementsByTagName("body")[0];
  var highlightNodeList = body.querySelectorAll("[data-highlight=yes]");
  for (var n = 0; n < highlightNodeList.length; n++) {
    var parentNode = highlightNodeList[n].parentNode;
    var childNodes = highlightNodeList[n].childNodes;
    var childNodesLen = childNodes.length;
    var nextSibling = highlightNodeList[n].nextSibling;
    for (var k = 0; k < childNodesLen; k++) {
      parentNode.insertBefore(childNodes[0], nextSibling);
    }
    var flagNode = document.createTextNode("");
    parentNode.replaceChild(flagNode, highlightNodeList[n]);
    parentNode.normalize();
  }
}
