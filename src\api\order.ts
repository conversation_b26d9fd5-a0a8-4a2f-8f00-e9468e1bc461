import { baseUrlApi } from "./utils";
import { http } from "@/utils/http";

// 会员产品列表
export function getOrderTypeListApi() {
  return http.request<any>("get", baseUrlApi("api/v1/product/members"));
}

// 创造订单
export function createOrderApi(data?: object) {
  return http.request<any>("post", baseUrlApi("api/v1/order/create"), {
    data
  });
}

// 校验订单状态
export function checkOrderStateApi(params?: { order_id: string }) {
  return http.request<any>(
    "get",
    baseUrlApi(`api/v1/order/state?order_id=${params?.order_id}`)
  );
}

// 文章信息
export function getDashboardUserInfoApi() {
  return http.request<any>("get", baseUrlApi(`api/v1/user/stats`));
}

// 当前账号统计信息
export function getVipAccountStatsApi() {
  return http.request<any>("get", baseUrlApi(`api/v1/user/stats`));
}

// 兑换会员
export function exchangeRedeemCodeApi(data?: object) {
  return http.request<any>(
    "post",
    baseUrlApi("api/v1/user/exchange_redeem_code"),
    {
      data
    }
  );
}
